from pydantic import BaseModel
from typing import List

import litserve as ls
from litserve.mcp import MCP

# https://github.com/deedy5/duckduckgo_search
from ddgs import DDGS


class AddRequest(BaseModel):
    a: int
    b: int


class TextRequest(BaseModel):
    text: str


class AddAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: AddRequest, **kwargs):
        return request

    def predict(self, x: AddRequest, *args, **kwargs):
        return {"result": x.a + x.b}


class ReverseAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: TextRequest, **kwargs):
        return request

    def predict(self, x: TextRequest, **kwargs):
        return {"reversed": x.text[::-1]}


class UpperAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: TextRequest, **kwargs):
        return request

    def predict(self, x: TextRequest, **kwargs):
        return {"upper": x.text.upper()}


class WebSearchResult(BaseModel):
    url: str
    title: str
    description: str | None = None


class WebSearchResponse(BaseModel):
    results: List[WebSearchResult]


class DuckDuckGoSearchAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request, **kwargs):
        print(request)
        return request

    def predict(self, request: TextRequest, **kwargs):
        
        search_results = []

        with DDGS() as ddgs:
            for result in ddgs.text(
                query=request.text, region="fr-fr", max_results=10, backend="lite"
            ):
                search_results.append(
                    WebSearchResult(
                        url=result.get("href", ""),
                        title=result.get("title", ""),
                        description=result.get("body", ""),
                    )
                )

        return WebSearchResponse(results=search_results)


if __name__ == "__main__":
    add_mcp = MCP(
        name="add_numbers", description="Ajoute deux nombres et retourne le résultat."
    )
    reverse_mcp = MCP(
        name="reverse_string", description="Inverse une chaîne de caractères donnée."
    )
    upper_mcp = MCP(
        name="uppercase_string",
        description="Convertit une chaîne de caractères donnée en majuscules.",
    )

    duckduckgo_mcp = MCP(
        name="duckduckgo_search",
        description="Effectue une recherche sur DuckDuckGo et retourne les résultats.",
        input_schema={
            "type": "object",
            "properties": {
                "text": {"type": "string", "description": "Texte de la recherche"}
            },
            "required": ["text"],
        },
    )

    server = ls.LitServer(
        [
            # AddAPI(mcp=add_mcp, api_path="/add"),
            # ReverseAPI(mcp=reverse_mcp, api_path="/reverse"),
            # UpperAPI(mcp=upper_mcp, api_path="/upper"),
            DuckDuckGoSearchAPI(mcp=duckduckgo_mcp, api_path="/websearch"),
        ],
    )

    server.run(port=8001)
